@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  :root {
    /* Brand Color Palette - Primary: #64003e */
    --background: 322 15% 98%;
    --foreground: 322 25% 15%;

    --card: 0 0% 100%;
    --card-foreground: 322 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 322 25% 15%;

    /* Brand Primary - Deep Burgundy #64003e */
    --primary: 322 100% 20%;
    --primary-foreground: 0 0% 100%;
    --primary-light: 322 80% 35%;
    --primary-dark: 322 100% 15%;

    /* Complementary Secondary - Soft Rose */
    --secondary: 322 25% 95%;
    --secondary-foreground: 322 30% 25%;

    --muted: 322 15% 96%;
    --muted-foreground: 322 10% 45%;

    /* Accent - Complementary Teal */
    --accent: 142 60% 45%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 75% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 322 15% 88%;
    --input: 322 15% 92%;
    --ring: 322 100% 20%;

    /* Brand Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(322 100% 20%), hsl(322 80% 35%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100%), hsl(322 15% 98%));
    --gradient-hero: linear-gradient(135deg, hsl(322 100% 20%) 0%, hsl(322 80% 35%) 50%, hsl(322 100% 15%) 100%);

    /* Professional Shadows */
    --shadow-sm: 0 2px 4px hsla(322, 25%, 10%, 0.08);
    --shadow-md: 0 4px 12px hsla(322, 25%, 10%, 0.12);
    --shadow-lg: 0 8px 24px hsla(322, 25%, 10%, 0.15);
    --shadow-card: 0 2px 8px hsla(322, 30%, 8%, 0.08);

    /* Status Colors */
    --success: 145 65% 50%;
    --success-foreground: 0 0% 100%;
    --warning: 45 90% 55%;
    --warning-foreground: 45 15% 15%;
    --info: 322 100% 20%;
    --info-foreground: 0 0% 100%;

    --radius: 0.75rem;

    --sidebar-background: 322 15% 98%;

    --sidebar-foreground: 322 25% 25%;

    --sidebar-primary: 322 100% 20%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 322 20% 95%;

    --sidebar-accent-foreground: 322 25% 25%;

    --sidebar-border: 322 15% 88%;

    --sidebar-ring: 322 100% 20%;
  }

  .dark {
    /* Dark Brand Color Palette */
    --background: 322 25% 8%;
    --foreground: 322 15% 95%;

    --card: 322 20% 12%;
    --card-foreground: 322 15% 95%;

    --popover: 322 20% 12%;
    --popover-foreground: 322 15% 95%;

    /* Dark Brand Primary - Lighter Burgundy */
    --primary: 322 80% 65%;
    --primary-foreground: 322 25% 8%;
    --primary-light: 322 85% 75%;
    --primary-dark: 322 75% 45%;

    --secondary: 322 15% 18%;
    --secondary-foreground: 322 15% 90%;

    --muted: 322 15% 15%;
    --muted-foreground: 322 10% 65%;

    --accent: 142 60% 65%;
    --accent-foreground: 322 25% 8%;

    --destructive: 0 75% 65%;
    --destructive-foreground: 322 25% 8%;

    --border: 322 15% 20%;
    --input: 322 15% 18%;
    --ring: 322 80% 65%;

    /* Dark Brand Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(322 80% 65%), hsl(322 85% 75%));
    --gradient-card: linear-gradient(145deg, hsl(322 20% 12%), hsl(322 25% 8%));
    --gradient-hero: linear-gradient(135deg, hsl(322 80% 65%) 0%, hsl(322 85% 75%) 50%, hsl(322 75% 45%) 100%);

    /* Dark Professional Shadows */
    --shadow-sm: 0 2px 4px hsla(322, 25%, 2%, 0.3);
    --shadow-md: 0 4px 12px hsla(322, 25%, 2%, 0.4);
    --shadow-lg: 0 8px 24px hsla(322, 25%, 2%, 0.5);
    --shadow-card: 0 2px 8px hsla(322, 30%, 2%, 0.4);

    /* Dark Status Colors */
    --success: 145 65% 60%;
    --success-foreground: 322 25% 8%;
    --warning: 45 90% 70%;
    --warning-foreground: 45 15% 15%;
    --info: 322 80% 65%;
    --info-foreground: 322 25% 8%;

    --sidebar-background: 322 25% 8%;
    --sidebar-foreground: 322 15% 90%;
    --sidebar-primary: 322 80% 65%;
    --sidebar-primary-foreground: 322 25% 8%;
    --sidebar-accent: 322 15% 15%;
    --sidebar-accent-foreground: 322 15% 90%;
    --sidebar-border: 322 15% 20%;
    --sidebar-ring: 322 80% 65%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Typography Specifications */
@layer components {
  /* Headline Typography */
  .headline {
    @apply text-headline font-semibold;
  }

  .headline-sm {
    @apply text-headline-sm font-semibold;
  }

  /* Subhead Typography */
  .subhead {
    @apply text-subhead font-medium;
  }

  .subhead-sm {
    @apply text-subhead-sm font-medium;
  }

  /* Paragraph Typography */
  .paragraph {
    @apply text-paragraph;
  }

  .paragraph-sm {
    @apply text-paragraph-sm;
  }

  .paragraph-xs {
    @apply text-paragraph-xs;
  }

  .paragraph-min {
    @apply text-paragraph-min;
  }

  /* Spacing Utilities */
  .spacing-bullet-tab {
    margin-left: theme('spacing.bullet-tab');
  }

  .spacing-head-subhead {
    margin-bottom: theme('spacing.head-subhead');
  }

  .spacing-head-paragraph {
    margin-bottom: theme('spacing.head-paragraph');
  }

  /* List Styling with Proper Bullet Spacing */
  .spec-list {
    @apply list-disc;
  }

  .spec-list li {
    @apply paragraph;
    margin-left: theme('spacing.bullet-tab');
  }

  .spec-list li::marker {
    color: hsl(var(--primary));
  }

  /* Content Structure Classes */
  .content-section {
    @apply space-y-step-1;
  }

  .content-section .headline {
    @apply spacing-head-subhead;
  }

  .content-section .subhead {
    @apply spacing-head-paragraph;
  }
}

/* RTL Support */
[dir="rtl"] {
  direction: rtl;
}

/* Force RTL for all content */
[dir="rtl"] * {
  direction: rtl;
  text-align: right;
}

/* RTL Layout Fixes */
[dir="rtl"] .flex:not(.flex-col):not(.flex-column) {
  flex-direction: row-reverse !important;
}

[dir="rtl"] .justify-between {
  justify-content: space-between;
}

[dir="rtl"] .text-left {
  text-align: right !important;
}

[dir="rtl"] .text-right {
  text-align: left !important;
}

/* Override specific flex directions */
[dir="rtl"] .flex {
  flex-direction: row-reverse !important;
}

[dir="rtl"] .flex-col {
  flex-direction: column !important;
}

/* RTL Spacing */
[dir="rtl"] .gap-2 > *:not(:last-child),
[dir="rtl"] .gap-3 > *:not(:last-child),
[dir="rtl"] .gap-4 > *:not(:last-child) {
  margin-left: 0;
  margin-right: var(--gap-size, 0.5rem);
}

/* RTL Margins and Padding */
[dir="rtl"] .ml-3 {
  margin-left: 0;
  margin-right: 0.75rem;
}

[dir="rtl"] .mr-3 {
  margin-right: 0;
  margin-left: 0.75rem;
}

[dir="rtl"] .pl-4 {
  padding-left: 0;
  padding-right: 1rem;
}

[dir="rtl"] .pr-4 {
  padding-right: 0;
  padding-left: 1rem;
}

/* RTL Border Radius */
[dir="rtl"] .rounded-l-lg {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}

[dir="rtl"] .rounded-r-lg {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}

/* Language switcher specific styles */
.language-switcher {
  transition: all 0.2s ease-in-out;
  position: relative;
}

.language-switcher:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px hsla(210, 25%, 10%, 0.15);
}

.language-switcher:active {
  transform: translateY(0);
}

/* Language badge animation */
.language-switcher .badge {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* Enhanced RTL support */
[dir="rtl"] .language-switcher {
  flex-direction: row-reverse;
}

[dir="rtl"] .flex {
  flex-direction: row-reverse;
}

/* Smooth transitions for language switching */
* {
  transition: margin 0.3s ease, padding 0.3s ease;
}

/* RTL Grid and Card Layouts */
[dir="rtl"] .grid {
  direction: rtl;
}

[dir="rtl"] .card {
  text-align: right;
}

[dir="rtl"] .justify-start {
  justify-content: flex-end;
}

[dir="rtl"] .justify-end {
  justify-content: flex-start;
}

/* RTL Specific Components */
[dir="rtl"] .space-x-2 > * + * {
  margin-left: 0;
  margin-right: 0.5rem;
}

[dir="rtl"] .space-x-4 > * + * {
  margin-left: 0;
  margin-right: 1rem;
}

/* RTL Chart and Analytics */
[dir="rtl"] .recharts-wrapper {
  direction: ltr; /* Keep charts in LTR for proper rendering */
}

[dir="rtl"] .highcharts-container {
  direction: ltr; /* Keep charts in LTR for proper rendering */
}

/* RTL Badge and Button Fixes */
[dir="rtl"] .badge {
  direction: rtl;
}

[dir="rtl"] .flex-row-reverse {
  flex-direction: row-reverse !important;
}

/* Body RTL/LTR classes for additional control */
body.rtl {
  direction: rtl;
}

body.ltr {
  direction: ltr;
}

/* RTL Header fixes */
[dir="rtl"] .header-content {
  flex-direction: row-reverse;
}

/* RTL Sidebar positioning */
[dir="rtl"] .sidebar {
  right: 0;
  left: auto;
}

/* RTL Main content adjustment */
[dir="rtl"] .main-content {
  margin-right: 0;
  margin-left: auto;
}

/* RTL Card content */
[dir="rtl"] .card-content {
  text-align: right;
}

/* RTL Progress bars */
[dir="rtl"] .progress-bar {
  transform: scaleX(-1);
}

/* RTL Dropdown positioning */
[dir="rtl"] .dropdown-content {
  right: 0;
  left: auto;
}

/* Force RTL layout for main containers */
[dir="rtl"] .h-screen {
  direction: rtl;
}

/* RTL Sidebar positioning using CSS order */
[dir="rtl"] .order-1 {
  order: 1;
}

[dir="rtl"] .order-2 {
  order: 2;
}

/* RTL specific overrides */
[dir="rtl"] .gap-2 {
  gap: 0.5rem;
  flex-direction: row-reverse;
}

[dir="rtl"] .gap-3 {
  gap: 0.75rem;
  flex-direction: row-reverse;
}

[dir="rtl"] .gap-4 {
  gap: 1rem;
  flex-direction: row-reverse;
}

/* RTL Card and content alignment */
[dir="rtl"] .card {
  direction: rtl;
  text-align: right;
}

[dir="rtl"] .card * {
  direction: rtl;
  text-align: right;
}

/* RTL Grid layouts */
[dir="rtl"] .grid {
  direction: rtl;
}

/* RTL Button groups */
[dir="rtl"] .btn-group {
  flex-direction: row-reverse;
}

/* RTL Navigation */
[dir="rtl"] nav {
  direction: rtl;
}

[dir="rtl"] .nav-item {
  text-align: right;
}

/* HTML RTL class targeting */
html.rtl {
  direction: rtl;
}

html.rtl * {
  direction: rtl;
}

html.rtl .flex:not(.flex-col) {
  flex-direction: row-reverse !important;
}

html.rtl .justify-start {
  justify-content: flex-end !important;
}

html.rtl .justify-end {
  justify-content: flex-start !important;
}

html.rtl .text-left {
  text-align: right !important;
}

html.rtl .text-right {
  text-align: left !important;
}

html.rtl .ml-3 {
  margin-left: 0 !important;
  margin-right: 0.75rem !important;
}

html.rtl .mr-3 {
  margin-right: 0 !important;
  margin-left: 0.75rem !important;
}

html.rtl .pl-4 {
  padding-left: 0 !important;
  padding-right: 1rem !important;
}

html.rtl .pr-4 {
  padding-right: 0 !important;
  padding-left: 1rem !important;
}

/* RTL Sidebar positioning */
html.rtl .sidebar-container {
  flex-direction: row-reverse !important;
}

html.rtl .main-content {
  order: 1;
}

html.rtl .sidebar {
  order: 2;
}