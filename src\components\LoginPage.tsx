import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { ArrowRight, User, GraduationCap, Shield, Crown, Settings, Check } from "lucide-react";
import loginBg from "@/assets/login_bg.jpg";
import logo from "@/assets/logo.png";

interface LoginPageProps {
  onLogin: (role: string) => void;
}

const LoginPage = ({ onLogin }: LoginPageProps) => {
  const [selectedRole, setSelectedRole] = useState<string>("");
  const [credentials, setCredentials] = useState({
    email: "",
    password: "",
  });

  const roles = [
    {
      value: "learner",
      label: "Learner",
      description: "Healthcare professional seeking training",
      icon: User,
      color: "from-blue-500 to-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
      hoverColor: "hover:border-blue-400",
    },
    {
      value: "trainer",
      label: "Trainer",
      description: "Training program instructor",
      icon: GraduationCap,
      color: "from-emerald-500 to-emerald-600",
      bgColor: "bg-emerald-50",
      borderColor: "border-emerald-200",
      hoverColor: "hover:border-emerald-400",
    },
    {
      value: "manager",
      label: "Manager",
      description: "Department head or supervisor",
      icon: Shield,
      color: "from-purple-500 to-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
      hoverColor: "hover:border-purple-400",
    },
    {
      value: "leadership",
      label: "Leadership",
      description: "Senior leadership team",
      icon: Crown,
      color: "from-amber-500 to-amber-600",
      bgColor: "bg-amber-50",
      borderColor: "border-amber-200",
      hoverColor: "hover:border-amber-400",
    },
    {
      value: "administrator",
      label: "Administrator",
      description: "System administrator",
      icon: Settings,
      color: "from-slate-500 to-slate-600",
      bgColor: "bg-slate-50",
      borderColor: "border-slate-200",
      hoverColor: "hover:border-slate-400",
    },
  ];

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (selectedRole && credentials.email && credentials.password) {
      onLogin(selectedRole);
    }
  };

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Background Image with Overlay Text */}
      <div className="hidden lg:flex lg:w-1/2 relative">
        {/* Background image */}
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: `url(${loginBg})`,
            backgroundSize: "cover",
            backgroundPosition: "center",
            backgroundRepeat: "no-repeat",
          }}
        />

        {/* Dark overlay */}
        <div className="absolute inset-0 bg-black/50" />

        {/* Text content */}
        <div className="relative z-10 flex flex-col justify-between h-full px-12 py-8 text-white">
          {/* Top-left text */}
          <div className="text-left">
            <h1 className="text-4xl font-bold">Namaa by Aster</h1>
          </div>

          {/* Bottom-left text */}
          <div className="text-left">
            <p className="text-lg font-medium italic">
              With knowledge and action, we elevate care
            </p>
          </div>
        </div>
      </div>

      {/* Right Side - Login Form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-8 bg-background">
        <div className="w-full max-w-md">
          {/* Logo and Welcome */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-6">
              <img
                src={logo}
                alt="Medcare Logo"
                className="h-24 w-auto object-contain"
              />
            </div>
            <h2 className="text-2xl font-semibold text-foreground mb-2">
              Welcome
            </h2>
            <p className="text-muted-foreground text-sm">
              Sign in to access your personalized dashboard
            </p>
          </div>

          {/* Login Form */}
          <Card className="relative bg-white/80 backdrop-blur-xl shadow-[0_20px_50px_rgba(8,_112,_184,_0.15)] border-0 rounded-3xl overflow-hidden group hover:shadow-[0_25px_60px_rgba(8,_112,_184,_0.2)] transition-all duration-500">
            {/* Subtle gradient border effect */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-100/50 via-transparent to-blue-50/30 rounded-3xl"></div>

            {/* Header with enhanced styling */}
            <CardHeader className="relative px-10 py-8 bg-gradient-to-br from-slate-50/90 to-blue-50/40">
              <div className="space-y-8">
                {/* Role Selection with card-based design */}
                <div className="space-y-4">
                  <Label className="text-base font-semibold text-slate-700 flex items-center gap-3 tracking-wide">
                    <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full shadow-sm"></div>
                    Select Your Role
                  </Label>
                  <div className="grid gap-3">
                    {roles.map((role) => {
                      const IconComponent = role.icon;
                      const isSelected = selectedRole === role.value;
                      return (
                        <button
                          key={role.value}
                          type="button"
                          onClick={() => setSelectedRole(role.value)}
                          className={`
                            relative w-full p-4 rounded-xl border-2 transition-all duration-300 text-left group
                            ${isSelected
                              ? `${role.borderColor} ${role.bgColor} shadow-md scale-[1.02]`
                              : `border-slate-200 bg-white/50 hover:bg-white/80 ${role.hoverColor} hover:shadow-sm hover:scale-[1.01]`
                            }
                          `}
                        >
                          <div className="flex items-center gap-4">
                            <div className={`
                              flex items-center justify-center w-12 h-12 rounded-xl transition-all duration-300
                              ${isSelected
                                ? `bg-gradient-to-r ${role.color} text-white shadow-lg`
                                : `bg-slate-100 text-slate-600 group-hover:bg-slate-200`
                              }
                            `}>
                              <IconComponent className="w-6 h-6" />
                            </div>
                            <div className="flex-1">
                              <div className={`font-semibold text-base transition-colors duration-300 ${
                                isSelected ? 'text-slate-800' : 'text-slate-700'
                              }`}>
                                {role.label}
                              </div>
                              <div className={`text-sm leading-relaxed transition-colors duration-300 ${
                                isSelected ? 'text-slate-600' : 'text-slate-500'
                              }`}>
                                {role.description}
                              </div>
                            </div>
                            {isSelected && (
                              <div className={`
                                flex items-center justify-center w-8 h-8 rounded-full bg-gradient-to-r ${role.color} text-white shadow-lg
                                animate-in zoom-in-50 duration-300
                              `}>
                                <Check className="w-4 h-4" />
                              </div>
                            )}
                          </div>
                        </button>
                      );
                    })}
                  </div>
                </div>

                {/* Animated form fields that appear after role selection */}
                {selectedRole && (
                  <div className="space-y-6 animate-in slide-in-from-top-4 duration-500">
                    {/* Email Address with enhanced styling */}
                    <div className="space-y-4">
                      <Label
                        htmlFor="email"
                        className="text-base font-semibold text-slate-700 flex items-center gap-3 tracking-wide"
                      >
                        <div className="w-3 h-3 bg-gradient-to-r from-emerald-500 to-emerald-600 rounded-full shadow-sm"></div>
                        Email Address
                      </Label>
                      <div className="relative group">
                        <Input
                          id="email"
                          type="email"
                          placeholder="Enter your professional email"
                          value={credentials.email}
                          onChange={(e) =>
                            setCredentials((prev) => ({
                              ...prev,
                              email: e.target.value,
                            }))
                          }
                          className="w-full h-14 border-2 border-slate-200/80 hover:border-blue-300 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 rounded-2xl transition-all duration-300 bg-white/70 backdrop-blur-sm px-5 text-base font-medium shadow-sm hover:shadow-md placeholder:text-slate-400"
                          required
                        />
                        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/5 to-emerald-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                      </div>
                    </div>

                    {/* Password with enhanced styling */}
                    <div className="space-y-4">
                      <Label
                        htmlFor="password"
                        className="text-base font-semibold text-slate-700 flex items-center gap-3 tracking-wide"
                      >
                        <div className="w-3 h-3 bg-gradient-to-r from-purple-500 to-purple-600 rounded-full shadow-sm"></div>
                        Password
                      </Label>
                      <div className="relative group">
                        <Input
                          id="password"
                          type="password"
                          placeholder="Enter your secure password"
                          value={credentials.password}
                          onChange={(e) =>
                            setCredentials((prev) => ({
                              ...prev,
                              password: e.target.value,
                            }))
                          }
                          className="w-full h-14 border-2 border-slate-200/80 hover:border-blue-300 focus:border-blue-500 focus:ring-4 focus:ring-blue-100 rounded-2xl transition-all duration-300 bg-white/70 backdrop-blur-sm px-5 text-base font-medium shadow-sm hover:shadow-md placeholder:text-slate-400"
                          required
                        />
                        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none"></div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardHeader>

            {/* Enhanced footer section */}
            {selectedRole && (
              <CardContent className="relative px-10 pb-10 pt-6 bg-gradient-to-br from-white/90 to-slate-50/50">
                <form onSubmit={handleLogin} className="space-y-8">
                  {/* Enhanced Get Started Button */}
                  <Button
                    type="submit"
                    className="w-full h-14 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white font-bold rounded-2xl transition-all duration-300 flex items-center justify-center gap-3 shadow-lg hover:shadow-xl transform hover:scale-[1.02] active:scale-[0.98] text-base tracking-wide disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
                    disabled={!credentials.email || !credentials.password}
                  >
                    <span>Get Started</span>
                    <ArrowRight className="w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" />
                  </Button>

                  {/* Enhanced Support Links */}
                  <div className="text-center space-y-5">
                    <button
                      type="button"
                      className="text-base text-blue-600 hover:text-blue-700 hover:underline font-semibold transition-all duration-200 hover:scale-105"
                    >
                      Forgot your password?
                    </button>
                    <div className="flex items-center justify-center gap-3 text-sm text-slate-500">
                      <div className="w-1.5 h-1.5 bg-slate-400 rounded-full"></div>
                      <span className="font-medium">Need assistance? Contact IT Support</span>
                      <div className="w-1.5 h-1.5 bg-slate-400 rounded-full"></div>
                    </div>
                  </div>
                </form>
              </CardContent>
            )}
          </Card>

          {/* Footer */}
          <div className="mt-8 text-center text-xs text-muted-foreground">
            <p>© 2025 Medcare LMS. All rights reserved.</p>
            <p className="mt-1">Secure • Professional • Trusted</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
